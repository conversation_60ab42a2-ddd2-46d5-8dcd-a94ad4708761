/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Archivo","arguments":[{"subsets":["latin"],"variable":"--font-archivo","display":"swap"}],"variableName":"archivo"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* vietnamese */
@font-face {
  font-family: 'Archivo';
  font-style: normal;
  font-weight: 100 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/b06b356f834173cc-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Archivo';
  font-style: normal;
  font-weight: 100 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/23d669af23d19c95-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Archivo';
  font-style: normal;
  font-weight: 100 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/1a4aa50920b5315c-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Archivo Fallback';src: local("Arial");ascent-override: 88.96%;descent-override: 21.28%;line-gap-override: 0.00%;size-adjust: 98.70%
}.__className_5afde0 {font-family: 'Archivo', 'Archivo Fallback';font-style: normal
}.__variable_5afde0 {--font-archivo: 'Archivo', 'Archivo Fallback'
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Lexend_Deca","arguments":[{"subsets":["latin"],"variable":"--font-lexend-deca","display":"swap"}],"variableName":"lexendDeca"} ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* vietnamese */
@font-face {
  font-family: 'Lexend Deca';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/f068b34e74a6df7a-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Lexend Deca';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/41e01f1a5faae04b-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Lexend Deca';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9789545a3447313c-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Lexend Deca Fallback';src: local("Arial");ascent-override: 90.98%;descent-override: 22.74%;line-gap-override: 0.00%;size-adjust: 109.91%
}.__className_9c5f6d {font-family: 'Lexend Deca', 'Lexend Deca Fallback';font-style: normal
}.__variable_9c5f6d {--font-lexend-deca: 'Lexend Deca', 'Lexend Deca Fallback'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: transparent;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem;
  }
  * {
  border-color: hsl(var(--border));
}
  
  body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
}
.container-responsive {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}
@media (min-width: 1400px) {

  .container-responsive {
    max-width: 1400px;
    padding-right: 4rem;
    padding-left: 4rem;
  }
}
.container-responsive {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}
@media (min-width: 640px) {

  .container-responsive {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
@media (min-width: 1024px) {

  .container-responsive {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
.section-padding {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
@media (min-width: 640px) {

  .section-padding {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}
@media (min-width: 1024px) {

  .section-padding {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.visible {
  visibility: visible;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-x-0 {
  left: 0px;
  right: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-12 {
  bottom: -3rem;
}
.-left-12 {
  left: -3rem;
}
.-right-12 {
  right: -3rem;
}
.-top-12 {
  top: -3rem;
}
.bottom-0 {
  bottom: 0px;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.left-4 {
  left: 1rem;
}
.left-\[50\%\] {
  left: 50%;
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-4 {
  right: 1rem;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-4 {
  top: 1rem;
}
.top-\[1px\] {
  top: 1px;
}
.top-\[50\%\] {
  top: 50%;
}
.top-\[60\%\] {
  top: 60%;
}
.top-full {
  top: 100%;
}
.z-10 {
  z-index: 10;
}
.z-50 {
  z-index: 50;
}
.z-\[1\] {
  z-index: 1;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.-ml-2 {
  margin-left: -0.5rem;
}
.-ml-4 {
  margin-left: -1rem;
}
.-mt-4 {
  margin-top: -1rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-\[-4\.00px\] {
  margin-bottom: -4.00px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-\[-4\.00px\] {
  margin-left: -4.00px;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-\[-1\.00px\] {
  margin-top: -1.00px;
}
.mt-\[-4\.00px\] {
  margin-top: -4.00px;
}
.mt-auto {
  margin-top: auto;
}
.block {
  display: block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-auto {
  aspect-ratio: auto;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[1px\] {
  height: 1px;
}
.h-\[calc\(100vh-4rem\)\] {
  height: calc(100vh - 4rem);
}
.h-\[var\(--radix-navigation-menu-viewport-height\)\] {
  height: var(--radix-navigation-menu-viewport-height);
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.min-h-\[80px\] {
  min-height: 80px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-8 {
  width: 2rem;
}
.w-\[1px\] {
  width: 1px;
}
.w-\[calc\(100vw-2rem\)\] {
  width: calc(100vw - 2rem);
}
.w-auto {
  width: auto;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-max {
  width: -moz-max-content;
  width: max-content;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-max {
  max-width: -moz-max-content;
  max-width: max-content;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-sm {
  max-width: 24rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.grow {
  flex-grow: 1;
}
.grow-0 {
  flex-grow: 0;
}
.basis-4\/5 {
  flex-basis: 80%;
}
.basis-full {
  flex-basis: 100%;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.cursor-default {
  cursor: default;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.list-none {
  list-style-type: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.self-stretch {
  align-self: stretch;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.text-balance {
  text-wrap: balance;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-\[100px\] {
  border-radius: 100px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-none {
  border-radius: 0px;
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-t-lg {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.rounded-tl-sm {
  border-top-left-radius: calc(var(--radius) - 4px);
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-4 {
  border-bottom-width: 4px;
}
.border-l {
  border-left-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-none {
  border-style: none;
}
.border-\[\#01010a26\] {
  border-color: #01010a26;
}
.border-\[\#12129c\] {
  --tw-border-opacity: 1;
  border-color: rgb(18 18 156 / var(--tw-border-opacity, 1));
}
.border-\[\#1717c4\] {
  --tw-border-opacity: 1;
  border-color: rgb(23 23 196 / var(--tw-border-opacity, 1));
}
.border-\[\#f2f2f2\] {
  --tw-border-opacity: 1;
  border-color: rgb(242 242 242 / var(--tw-border-opacity, 1));
}
.border-input {
  border-color: hsl(var(--input));
}
.border-primary {
  border-color: hsl(var(--primary));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.bg-\[\#01010a0d\] {
  background-color: #01010a0d;
}
.bg-\[\#1717c4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(23 23 196 / var(--tw-bg-opacity, 1));
}
.bg-\[\#f2f2f2\] {
  --tw-bg-opacity: 1;
  background-color: rgb(242 242 242 / var(--tw-bg-opacity, 1));
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-background\/80 {
  background-color: hsl(var(--background) / 0.8);
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-border {
  background-color: hsl(var(--border));
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-destructive {
  background-color: hsl(var(--destructive));
}
.bg-muted {
  background-color: hsl(var(--muted));
}
.bg-popover {
  background-color: hsl(var(--popover));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-4 {
  padding-top: 1rem;
}
.text-center {
  text-align: center;
}
.font-heading-h1 {
  font-family: var(--heading-h1-font-family);
}
.font-heading-h2 {
  font-family: var(--heading-h2-font-family);
}
.font-heading-h4 {
  font-family: var(--heading-h4-font-family);
}
.font-heading-h5 {
  font-family: var(--heading-h5-font-family);
}
.font-heading-tagline {
  font-family: var(--heading-tagline-font-family);
}
.font-text-large-semi-bold {
  font-family: var(--text-large-semi-bold-font-family);
}
.font-text-medium-normal {
  font-family: var(--text-medium-normal-font-family);
}
.font-text-medium-semi-bold {
  font-family: var(--text-medium-semi-bold-font-family);
}
.font-text-regular-link {
  font-family: var(--text-regular-link-font-family);
}
.font-text-regular-medium {
  font-family: var(--text-regular-medium-font-family);
}
.font-text-regular-normal {
  font-family: var(--text-regular-normal-font-family);
}
.font-text-regular-semi-bold {
  font-family: var(--text-regular-semi-bold-font-family);
}
.font-text-small-link {
  font-family: var(--text-small-link-font-family);
}
.font-text-small-normal {
  font-family: var(--text-small-normal-font-family);
}
.font-text-small-semi-bold {
  font-family: var(--text-small-semi-bold-font-family);
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-\[length\:var\(--heading-h1-font-size\)\] {
  font-size: var(--heading-h1-font-size);
}
.text-\[length\:var\(--heading-h2-font-size\)\] {
  font-size: var(--heading-h2-font-size);
}
.text-\[length\:var\(--heading-h4-font-size\)\] {
  font-size: var(--heading-h4-font-size);
}
.text-\[length\:var\(--heading-h5-font-size\)\] {
  font-size: var(--heading-h5-font-size);
}
.text-\[length\:var\(--heading-tagline-font-size\)\] {
  font-size: var(--heading-tagline-font-size);
}
.text-\[length\:var\(--text-large-semi-bold-font-size\)\] {
  font-size: var(--text-large-semi-bold-font-size);
}
.text-\[length\:var\(--text-medium-normal-font-size\)\] {
  font-size: var(--text-medium-normal-font-size);
}
.text-\[length\:var\(--text-medium-semi-bold-font-size\)\] {
  font-size: var(--text-medium-semi-bold-font-size);
}
.text-\[length\:var\(--text-regular-link-font-size\)\] {
  font-size: var(--text-regular-link-font-size);
}
.text-\[length\:var\(--text-regular-medium-font-size\)\] {
  font-size: var(--text-regular-medium-font-size);
}
.text-\[length\:var\(--text-regular-normal-font-size\)\] {
  font-size: var(--text-regular-normal-font-size);
}
.text-\[length\:var\(--text-regular-semi-bold-font-size\)\] {
  font-size: var(--text-regular-semi-bold-font-size);
}
.text-\[length\:var\(--text-small-link-font-size\)\] {
  font-size: var(--text-small-link-font-size);
}
.text-\[length\:var\(--text-small-normal-font-size\)\] {
  font-size: var(--text-small-normal-font-size);
}
.text-\[length\:var\(--text-small-semi-bold-font-size\)\] {
  font-size: var(--text-small-semi-bold-font-size);
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-\[number\:var\(--heading-h1-font-weight\)\] {
  font-weight: var(--heading-h1-font-weight);
}
.font-\[number\:var\(--heading-h2-font-weight\)\] {
  font-weight: var(--heading-h2-font-weight);
}
.font-\[number\:var\(--heading-h4-font-weight\)\] {
  font-weight: var(--heading-h4-font-weight);
}
.font-\[number\:var\(--heading-tagline-font-weight\)\] {
  font-weight: var(--heading-tagline-font-weight);
}
.font-\[number\:var\(--text-large-semi-bold-font-weight\)\] {
  font-weight: var(--text-large-semi-bold-font-weight);
}
.font-\[number\:var\(--text-medium-normal-font-weight\)\] {
  font-weight: var(--text-medium-normal-font-weight);
}
.font-\[number\:var\(--text-medium-semi-bold-font-weight\)\] {
  font-weight: var(--text-medium-semi-bold-font-weight);
}
.font-\[number\:var\(--text-regular-link-font-weight\)\] {
  font-weight: var(--text-regular-link-font-weight);
}
.font-\[number\:var\(--text-regular-medium-font-weight\)\] {
  font-weight: var(--text-regular-medium-font-weight);
}
.font-\[number\:var\(--text-regular-normal-font-weight\)\] {
  font-weight: var(--text-regular-normal-font-weight);
}
.font-\[number\:var\(--text-regular-semi-bold-font-weight\)\] {
  font-weight: var(--text-regular-semi-bold-font-weight);
}
.font-\[number\:var\(--text-small-normal-font-weight\)\] {
  font-weight: var(--text-small-normal-font-weight);
}
.font-\[number\:var\(--text-small-semi-bold-font-weight\)\] {
  font-weight: var(--text-small-semi-bold-font-weight);
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.leading-\[var\(--heading-h1-line-height\)\] {
  line-height: var(--heading-h1-line-height);
}
.leading-\[var\(--heading-h2-line-height\)\] {
  line-height: var(--heading-h2-line-height);
}
.leading-\[var\(--heading-h4-line-height\)\] {
  line-height: var(--heading-h4-line-height);
}
.leading-\[var\(--heading-h5-line-height\)\] {
  line-height: var(--heading-h5-line-height);
}
.leading-\[var\(--heading-tagline-line-height\)\] {
  line-height: var(--heading-tagline-line-height);
}
.leading-\[var\(--text-large-semi-bold-line-height\)\] {
  line-height: var(--text-large-semi-bold-line-height);
}
.leading-\[var\(--text-medium-normal-line-height\)\] {
  line-height: var(--text-medium-normal-line-height);
}
.leading-\[var\(--text-medium-semi-bold-line-height\)\] {
  line-height: var(--text-medium-semi-bold-line-height);
}
.leading-\[var\(--text-regular-link-line-height\)\] {
  line-height: var(--text-regular-link-line-height);
}
.leading-\[var\(--text-regular-medium-line-height\)\] {
  line-height: var(--text-regular-medium-line-height);
}
.leading-\[var\(--text-regular-normal-line-height\)\] {
  line-height: var(--text-regular-normal-line-height);
}
.leading-\[var\(--text-regular-semi-bold-line-height\)\] {
  line-height: var(--text-regular-semi-bold-line-height);
}
.leading-\[var\(--text-small-link-line-height\)\] {
  line-height: var(--text-small-link-line-height);
}
.leading-\[var\(--text-small-normal-line-height\)\] {
  line-height: var(--text-small-normal-line-height);
}
.leading-\[var\(--text-small-semi-bold-line-height\)\] {
  line-height: var(--text-small-semi-bold-line-height);
}
.leading-none {
  line-height: 1;
}
.tracking-\[var\(--heading-h1-letter-spacing\)\] {
  letter-spacing: var(--heading-h1-letter-spacing);
}
.tracking-\[var\(--heading-h2-letter-spacing\)\] {
  letter-spacing: var(--heading-h2-letter-spacing);
}
.tracking-\[var\(--heading-h4-letter-spacing\)\] {
  letter-spacing: var(--heading-h4-letter-spacing);
}
.tracking-\[var\(--heading-h5-letter-spacing\)\] {
  letter-spacing: var(--heading-h5-letter-spacing);
}
.tracking-\[var\(--heading-tagline-letter-spacing\)\] {
  letter-spacing: var(--heading-tagline-letter-spacing);
}
.tracking-\[var\(--text-large-semi-bold-letter-spacing\)\] {
  letter-spacing: var(--text-large-semi-bold-letter-spacing);
}
.tracking-\[var\(--text-medium-normal-letter-spacing\)\] {
  letter-spacing: var(--text-medium-normal-letter-spacing);
}
.tracking-\[var\(--text-medium-semi-bold-letter-spacing\)\] {
  letter-spacing: var(--text-medium-semi-bold-letter-spacing);
}
.tracking-\[var\(--text-regular-link-letter-spacing\)\] {
  letter-spacing: var(--text-regular-link-letter-spacing);
}
.tracking-\[var\(--text-regular-medium-letter-spacing\)\] {
  letter-spacing: var(--text-regular-medium-letter-spacing);
}
.tracking-\[var\(--text-regular-normal-letter-spacing\)\] {
  letter-spacing: var(--text-regular-normal-letter-spacing);
}
.tracking-\[var\(--text-regular-semi-bold-letter-spacing\)\] {
  letter-spacing: var(--text-regular-semi-bold-letter-spacing);
}
.tracking-\[var\(--text-small-link-letter-spacing\)\] {
  letter-spacing: var(--text-small-link-letter-spacing);
}
.tracking-\[var\(--text-small-normal-letter-spacing\)\] {
  letter-spacing: var(--text-small-normal-letter-spacing);
}
.tracking-\[var\(--text-small-semi-bold-letter-spacing\)\] {
  letter-spacing: var(--text-small-semi-bold-letter-spacing);
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.text-\[\#01010a99\] {
  color: #01010a99;
}
.text-\[\#01010a\] {
  --tw-text-opacity: 1;
  color: rgb(1 1 10 / var(--tw-text-opacity, 1));
}
.text-\[\#1717c4\] {
  --tw-text-opacity: 1;
  color: rgb(23 23 196 / var(--tw-text-opacity, 1));
}
.text-card-foreground {
  color: hsl(var(--card-foreground));
}
.text-current {
  color: currentColor;
}
.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-70 {
  opacity: 0.7;
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.text-balance {
    text-wrap: balance;
  }
.\[border-bottom-style\:solid\] {
  border-bottom-style: solid;
}
.\[font-style\:var\(--heading-h1-font-style\)\] {
  font-style: var(--heading-h1-font-style);
}
.\[font-style\:var\(--heading-h2-font-style\)\] {
  font-style: var(--heading-h2-font-style);
}
.\[font-style\:var\(--heading-h4-font-style\)\] {
  font-style: var(--heading-h4-font-style);
}
.\[font-style\:var\(--heading-h5-font-style\)\] {
  font-style: var(--heading-h5-font-style);
}
.\[font-style\:var\(--heading-tagline-font-style\)\] {
  font-style: var(--heading-tagline-font-style);
}
.\[font-style\:var\(--text-large-semi-bold-font-style\)\] {
  font-style: var(--text-large-semi-bold-font-style);
}
.\[font-style\:var\(--text-medium-normal-font-style\)\] {
  font-style: var(--text-medium-normal-font-style);
}
.\[font-style\:var\(--text-medium-semi-bold-font-style\)\] {
  font-style: var(--text-medium-semi-bold-font-style);
}
.\[font-style\:var\(--text-regular-link-font-style\)\] {
  font-style: var(--text-regular-link-font-style);
}
.\[font-style\:var\(--text-regular-medium-font-style\)\] {
  font-style: var(--text-regular-medium-font-style);
}
.\[font-style\:var\(--text-regular-normal-font-style\)\] {
  font-style: var(--text-regular-normal-font-style);
}
.\[font-style\:var\(--text-regular-semi-bold-font-style\)\] {
  font-style: var(--text-regular-semi-bold-font-style);
}
.\[font-style\:var\(--text-small-normal-font-style\)\] {
  font-style: var(--text-small-normal-font-style);
}
.\[font-style\:var\(--text-small-semi-bold-font-style\)\] {
  font-style: var(--text-small-semi-bold-font-style);
}

:root {
  --heading-h1-font-family: var(--font-archivo), "Archivo", Helvetica, sans-serif;
  --heading-h1-font-size: clamp(2rem, 5vw, 3.5rem);
  --heading-h1-font-style: normal;
  --heading-h1-font-weight: 400;
  --heading-h1-letter-spacing: -0.02em;
  --heading-h1-line-height: 1.2;
  
  --heading-h2-font-family: var(--font-archivo), "Archivo", Helvetica, sans-serif;
  --heading-h2-font-size: clamp(1.75rem, 4vw, 3rem);
  --heading-h2-font-style: normal;
  --heading-h2-font-weight: 400;
  --heading-h2-letter-spacing: -0.01em;
  --heading-h2-line-height: 1.2;
  
  --heading-h4-font-family: var(--font-archivo), "Archivo", Helvetica, sans-serif;
  --heading-h4-font-size: clamp(1.25rem, 2.5vw, 2rem);
  --heading-h4-font-style: normal;
  --heading-h4-font-weight: 400;
  --heading-h4-letter-spacing: -0.01em;
  --heading-h4-line-height: 1.3;
  
  --heading-h5-font-family: var(--font-archivo), "Archivo", Helvetica, sans-serif;
  --heading-h5-font-size: clamp(1.125rem, 2vw, 1.5rem);
  --heading-h5-font-style: normal;
  --heading-h5-font-weight: 400;
  --heading-h5-letter-spacing: -0.01em;
  --heading-h5-line-height: 1.4;
  
  --heading-tagline-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --heading-tagline-font-size: 1rem;
  --heading-tagline-font-style: normal;
  --heading-tagline-font-weight: 600;
  --heading-tagline-letter-spacing: 0px;
  --heading-tagline-line-height: 1.5;
  
  --text-large-semi-bold-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-large-semi-bold-font-size: clamp(1.125rem, 1.5vw, 1.25rem);
  --text-large-semi-bold-font-style: normal;
  --text-large-semi-bold-font-weight: 600;
  --text-large-semi-bold-letter-spacing: 0px;
  --text-large-semi-bold-line-height: 1.5;
  
  --text-medium-normal-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-medium-normal-font-size: clamp(1rem, 1.25vw, 1.125rem);
  --text-medium-normal-font-style: normal;
  --text-medium-normal-font-weight: 400;
  --text-medium-normal-letter-spacing: 0px;
  --text-medium-normal-line-height: 1.5;
  
  --text-medium-semi-bold-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-medium-semi-bold-font-size: clamp(1rem, 1.25vw, 1.125rem);
  --text-medium-semi-bold-font-style: normal;
  --text-medium-semi-bold-font-weight: 600;
  --text-medium-semi-bold-letter-spacing: 0px;
  --text-medium-semi-bold-line-height: 1.5;
  
  --text-regular-link-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-regular-link-font-size: 1rem;
  --text-regular-link-font-style: normal;
  --text-regular-link-font-weight: 400;
  --text-regular-link-letter-spacing: 0px;
  --text-regular-link-line-height: 1.5;
  
  --text-regular-medium-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-regular-medium-font-size: 1rem;
  --text-regular-medium-font-style: normal;
  --text-regular-medium-font-weight: 500;
  --text-regular-medium-letter-spacing: 0px;
  --text-regular-medium-line-height: 1.5;
  
  --text-regular-normal-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-regular-normal-font-size: 1rem;
  --text-regular-normal-font-style: normal;
  --text-regular-normal-font-weight: 400;
  --text-regular-normal-letter-spacing: 0px;
  --text-regular-normal-line-height: 1.5;
  
  --text-regular-semi-bold-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-regular-semi-bold-font-size: 1rem;
  --text-regular-semi-bold-font-style: normal;
  --text-regular-semi-bold-font-weight: 600;
  --text-regular-semi-bold-letter-spacing: 0px;
  --text-regular-semi-bold-line-height: 1.5;
  
  --text-small-link-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-small-link-font-size: 0.875rem;
  --text-small-link-font-style: normal;
  --text-small-link-font-weight: 400;
  --text-small-link-letter-spacing: 0px;
  --text-small-link-line-height: 1.5;
  
  --text-small-normal-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-small-normal-font-size: 0.875rem;
  --text-small-normal-font-style: normal;
  --text-small-normal-font-weight: 400;
  --text-small-normal-letter-spacing: 0px;
  --text-small-normal-line-height: 1.5;
  
  --text-small-semi-bold-font-family: var(--font-lexend-deca), "Lexend Deca", Helvetica, sans-serif;
  --text-small-semi-bold-font-size: 0.875rem;
  --text-small-semi-bold-font-style: normal;
  --text-small-semi-bold-font-weight: 600;
  --text-small-semi-bold-letter-spacing: 0px;
  --text-small-semi-bold-line-height: 1.5;
}

/* Carousel specific styles */
.embla {
  overflow: hidden;
}

.embla__container {
  display: flex;
}

.embla__slide {
  flex: 0 0 auto;
  min-width: 0;
}

/* Enhanced Mobile menu animations */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%) translateY(-50%) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateX(0) translateY(-50%) scale(1);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0) translateY(-50%) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(100%) translateY(-50%) scale(0.95);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes zoomIn {
  from {
    transform: scale(0.95);
  }
  to {
    transform: scale(1);
  }
}

@keyframes zoomOut {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.95);
  }
}

.mobile-menu-enter {
  animation: slideInFromRight 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.mobile-menu-exit {
  animation: slideOutToRight 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Smooth scrolling for mobile */
@media (max-width: 768px) {
  html {
    scroll-behavior: smooth;
  }
}

/* Touch-friendly button sizes */
@media (max-width: 768px) {
  button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Custom backdrop blur for mobile menu */
.mobile-menu-backdrop {
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.1);
}
.file\:border-0::file-selector-button {
  border-width: 0px;
}
.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}
.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.file\:font-medium::file-selector-button {
  font-weight: 500;
}
.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}
.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}
.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-\[1\.05\]:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:border-\[\#01010a26\]:hover {
  border-color: #01010a26;
}
.hover\:bg-\[\#01010a0d\]:hover {
  background-color: #01010a0d;
}
.hover\:bg-\[\#01010a1a\]:hover {
  background-color: #01010a1a;
}
.hover\:bg-\[\#1414a8\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(20 20 168 / var(--tw-bg-opacity, 1));
}
.hover\:bg-\[\#f2f2f2\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(242 242 242 / var(--tw-bg-opacity, 1));
}
.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}
.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}
.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}
.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}
.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white\/90:hover {
  background-color: rgb(255 255 255 / 0.9);
}
.hover\:text-\[\#1717c4\]:hover {
  --tw-text-opacity: 1;
  color: rgb(23 23 196 / var(--tw-text-opacity, 1));
}
.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:no-underline:hover {
  text-decoration-line: none;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:border-\[\#1717c4\]:focus {
  --tw-border-opacity: 1;
  border-color: rgb(23 23 196 / var(--tw-border-opacity, 1));
}
.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}
.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-\[\#1717c4\]:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(23 23 196 / var(--tw-ring-opacity, 1));
}
.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}
.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}
.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}
.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}
.active\:scale-\[0\.95\]:active {
  --tw-scale-x: 0.95;
  --tw-scale-y: 0.95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.active\:scale-\[0\.98\]:active {
  --tw-scale-x: 0.98;
  --tw-scale-y: 0.98;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}
.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}
.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[active\]\:bg-accent\/50[data-active] {
  background-color: hsl(var(--accent) / 0.5);
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary));
}
.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
  background-color: hsl(var(--accent) / 0.5);
}
.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: hsl(var(--secondary));
}
.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
  color: hsl(var(--primary-foreground));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}
.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: 500ms;
}
.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@media (min-width: 640px) {

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:h-24 {
    height: 6rem;
  }

  .sm\:h-64 {
    height: 16rem;
  }

  .sm\:h-80 {
    height: 20rem;
  }

  .sm\:h-96 {
    height: 24rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:basis-1\/2 {
    flex-basis: 50%;
  }

  .sm\:basis-3\/5 {
    flex-basis: 60%;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:rounded-lg {
    border-radius: var(--radius);
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:text-left {
    text-align: left;
  }
}
@media (min-width: 768px) {

  .md\:absolute {
    position: absolute;
  }

  .md\:-ml-4 {
    margin-left: -1rem;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-80 {
    height: 20rem;
  }

  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
    width: var(--radix-navigation-menu-viewport-width);
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:pl-4 {
    padding-left: 1rem;
  }
}
@media (min-width: 1024px) {

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:mb-4 {
    margin-bottom: 1rem;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-12 {
    height: 3rem;
  }

  .lg\:h-14 {
    height: 3.5rem;
  }

  .lg\:h-16 {
    height: 4rem;
  }

  .lg\:h-32 {
    height: 8rem;
  }

  .lg\:h-6 {
    height: 1.5rem;
  }

  .lg\:h-80 {
    height: 20rem;
  }

  .lg\:h-9 {
    height: 2.25rem;
  }

  .lg\:h-\[182px\] {
    height: 182px;
  }

  .lg\:h-\[320px\] {
    height: 320px;
  }

  .lg\:h-\[486px\] {
    height: 486px;
  }

  .lg\:h-\[500px\] {
    height: 500px;
  }

  .lg\:h-\[600px\] {
    height: 600px;
  }

  .lg\:h-\[61px\] {
    height: 61px;
  }

  .lg\:h-\[640px\] {
    height: 640px;
  }

  .lg\:h-\[720px\] {
    height: 720px;
  }

  .lg\:h-\[72px\] {
    height: 72px;
  }

  .lg\:h-\[734px\] {
    height: 734px;
  }

  .lg\:h-auto {
    height: auto;
  }

  .lg\:w-12 {
    width: 3rem;
  }

  .lg\:w-14 {
    width: 3.5rem;
  }

  .lg\:w-16 {
    width: 4rem;
  }

  .lg\:w-6 {
    width: 1.5rem;
  }

  .lg\:w-\[120px\] {
    width: 120px;
  }

  .lg\:w-\[380px\] {
    width: 380px;
  }

  .lg\:w-\[84px\] {
    width: 84px;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:w-\[100px\] {
    width: 100px;
  }

  .lg\:w-\[150px\] {
    width: 150px;
  }

  .lg\:w-\[2000px\] {
    width: 2000px;
  }

  .lg\:w-\[200px\] {
    width: 200px;
  }

  .lg\:max-w-\[400px\] {
    max-width: 400px;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-end {
    align-items: flex-end;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-16 {
    gap: 4rem;
  }

  .lg\:gap-20 {
    gap: 5rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:gap-5 {
    gap: 1.25rem;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:pb-4 {
    padding-bottom: 1rem;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}
.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
